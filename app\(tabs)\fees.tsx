import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, StyleSheet } from 'react-native';

import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { FuturisticButton } from '@/components/ui/FuturisticButton';
import { FuturisticCard } from '@/components/ui/FuturisticCard';
import { FuturisticGrid } from '@/components/ui/FuturisticGrid';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAppTheme } from '@/contexts/ThemeContext';
import { getCurrencyIcon, useCurrency } from '../../utils/currency';
import { getResponsiveFontSize, getResponsiveSpacing, isSmallScreen, responsiveDimensions } from '../../utils/responsive';

// Mock data for fees - would be replaced with actual data
const PENDING_FEES = [
  {
    id: '1',
    app: 'Instagram',
    amount: 5,
    timestamp: '2024-01-15T10:30:00Z',
    duration: 15,
    status: 'pending'
  },
  {
    id: '2',
    app: 'Twitter',
    amount: 2,
    timestamp: '2024-01-15T09:15:00Z',
    duration: 8,
    status: 'pending'
  },
  {
    id: '3',
    app: 'YouTube',
    amount: 3,
    timestamp: '2024-01-14T16:45:00Z',
    duration: 22,
    status: 'pending'
  },
];

const PAID_FEES = [
  {
    id: '4',
    app: 'TikTok',
    amount: 4,
    timestamp: '2024-01-13T14:20:00Z',
    duration: 18,
    status: 'paid',
    paidAt: '2024-01-14T08:00:00Z'
  },
  {
    id: '5',
    app: 'Instagram',
    amount: 5,
    timestamp: '2024-01-12T11:30:00Z',
    duration: 25,
    status: 'paid',
    paidAt: '2024-01-13T09:15:00Z'
  },
];

export default function FeesScreen() {
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t, i18n } = useTranslation();
  const { formatCurrency } = useCurrency();

  const [selectedTab, setSelectedTab] = useState<'pending' | 'paid'>('pending');

  // Initial empty state
  const totalPending = 0;
  const totalPaid = 0;
  const pendingFees: any[] = [];
  const paidFees: any[] = [];

  const formatDate = (timestamp: string) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const payFee = (feeId: string) => {
    const fee = pendingFees.find(f => f.id === feeId);
    if (!fee) return;

    Alert.alert(
      t('fees.payFeeTitle'),
      t('fees.payFeeConfirm', { app: fee.app, amount: formatCurrency(fee.amount) }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('fees.payNow'),
          onPress: () => {
            // In a real app, this would integrate with payment processing
            setPendingFees(pendingFees.filter(f => f.id !== feeId));
            Alert.alert(t('fees.paymentSuccess'), t('fees.paymentSuccessDesc'));
          }
        }
      ]
    );
  };

  const payAllFees = () => {
    if (pendingFees.length === 0) return;

    Alert.alert(
      t('fees.payAllFeesTitle'),
      t('fees.payAllFeesConfirm', { total: formatCurrency(totalPending) }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('fees.payAll'),
          onPress: () => {
            setPendingFees([]);
            Alert.alert(t('fees.paymentSuccess'), t('fees.allFeesPaymentSuccessDesc'));
          }
        }
      ]
    );
  };

  const disputeFee = (feeId: string) => {
    const fee = pendingFees.find(f => f.id === feeId);
    if (!fee) return;

    Alert.alert(
      t('fees.disputeFeeTitle'),
      t('fees.disputeFeeDesc', { app: fee.app }),
      [
        { text: t('common.cancel'), style: 'cancel' },
        {
          text: t('fees.submitDispute'),
          onPress: () => {
            Alert.alert(t('fees.disputeSubmitted'), t('fees.disputeSubmittedDesc'));
          }
        }
      ]
    );
  };

  return (
    <ThemedView style={styles.container}>
      <FuturisticGrid size={40} opacity={0.1} />
      
      <ParallaxScrollView
        headerBackgroundColor={{
          light: Colors.light.background,
          dark: Colors.dark.background
        }}
        headerImage={
          <ThemedView style={styles.headerImageContainer}>
            <IconSymbol
              size={responsiveDimensions.header.iconSize}
              name={getCurrencyIcon(i18n.language)}
              color={colors.tint}
            />
            <ThemedText type="title" style={[styles.headerTitle, { color: colors.tint }]}>
              {t('fees.title')}
            </ThemedText>
            <ThemedText style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
              {t('subtitles.fees')}
            </ThemedText>
          </ThemedView>
        }>
        <ThemedView style={styles.content}>
          {/* Stats Grid */}
          <ThemedView style={styles.statsGrid}>
            <FuturisticCard variant="neon" style={styles.statCard} glowIntensity={1.2}>
              <IconSymbol size={24} name="exclamationmark.circle.fill" color={colors.tint} />
              <ThemedText style={[styles.statNumber, { color: colors.tint }]}>
                {formatCurrency(totalPending)}
              </ThemedText>
              <ThemedText style={styles.statLabel}>
                {t('fees.pendingFees')}
              </ThemedText>
            </FuturisticCard>

            <FuturisticCard variant="glass" style={styles.statCard}>
              <IconSymbol size={24} name="checkmark.circle.fill" color={colors.secondary} />
              <ThemedText style={[styles.statNumber, { color: colors.secondary }]}>
                {formatCurrency(totalPaid)}
              </ThemedText>
              <ThemedText style={styles.statLabel}>
                {t('fees.paidFees')}
              </ThemedText>
            </FuturisticCard>
          </ThemedView>

          {/* Tab Navigation */}
          <ThemedView style={styles.tabContainer}>
            <FuturisticButton
              title={`${t('fees.pending')} (${pendingFees.length})`}
              variant={selectedTab === 'pending' ? 'neon' : 'outline'}
              onPress={() => setSelectedTab('pending')}
              glowIntensity={selectedTab === 'pending' ? 1.5 : 0.5}
              style={styles.tabButton}
            />

            <FuturisticButton
              title={`${t('fees.paid')} (${paidFees.length})`}
              variant={selectedTab === 'paid' ? 'neon' : 'outline'}
              onPress={() => setSelectedTab('paid')}
              glowIntensity={selectedTab === 'paid' ? 1.5 : 0.5}
              style={styles.tabButton}
            />
          </ThemedView>

          {/* Pay All Button */}
          {pendingFees.length > 0 && (
            <FuturisticButton
              title={t('fees.payAllFees', { total: formatCurrency(totalPending) })}
              variant="neon"
              size="large"
              onPress={payAllFees}
              glowIntensity={2}
              style={styles.payAllButton}
            />
          )}

          {/* Fees List Section */}
          <ThemedView style={styles.section}>
            <ThemedText style={[styles.sectionTitle, { color: colors.tint }]}>
              {selectedTab === 'pending' ? 'Pending Transactions' : 'Payment History'}
            </ThemedText>
            
            {selectedTab === 'pending' ? (
              pendingFees.length > 0 ? (
                pendingFees.map((fee) => (
                  <FuturisticCard
                    key={fee.id}
                    variant="glass"
                    style={styles.feeItem}
                    glowIntensity={1}
                  >
                    <ThemedView style={styles.feeHeader}>
                      <ThemedView style={styles.feeInfo}>
                        <ThemedText style={styles.feeApp}>
                          {fee.app}
                        </ThemedText>
                        <ThemedText style={[styles.feeDate, { color: colors.textMuted }]}>
                          {formatDate(fee.timestamp)} at {formatTime(fee.timestamp)}
                        </ThemedText>
                        <ThemedText style={[styles.feeDuration, { color: colors.textMuted }]}>
                          {t('fees.usageDuration', { duration: fee.duration })}
                        </ThemedText>
                      </ThemedView>
                      <ThemedText style={[styles.feeAmount, { color: colors.tint }]}>
                        {formatCurrency(fee.amount)}
                      </ThemedText>
                    </ThemedView>

                    <ThemedView style={styles.feeActions}>
                      <FuturisticButton
                        title={t('fees.payNow')}
                        variant="neon"
                        size="small"
                        onPress={() => payFee(fee.id)}
                        style={styles.actionButton}
                      />

                      <FuturisticButton
                        title={t('fees.dispute')}
                        variant="outline"
                        size="small"
                        onPress={() => disputeFee(fee.id)}
                        style={styles.actionButton}
                      />
                    </ThemedView>
                  </FuturisticCard>
                ))
              ) : (
                <FuturisticCard variant="glass" style={styles.emptyState}>
                  <IconSymbol size={64} name="checkmark.circle.fill" color={colors.tint} />
                  <ThemedText style={[styles.emptyTitle, { color: colors.tint }]}>
                    {t('fees.noPendingFees')}
                  </ThemedText>
                  <ThemedText style={[styles.emptyDesc, { color: colors.textSecondary }]}>
                    {t('fees.noPendingFeesDesc')}
                  </ThemedText>
                </FuturisticCard>
              )
            ) : (
              paidFees.map((fee) => (
                <FuturisticCard
                  key={fee.id}
                  variant="glass"
                  style={styles.feeItem}
                  glowIntensity={0.5}
                >
                  <ThemedView style={styles.feeHeader}>
                    <ThemedView style={styles.feeInfo}>
                      <ThemedText style={styles.feeApp}>
                        {fee.app}
                      </ThemedText>
                      <ThemedText style={[styles.feeDate, { color: colors.textMuted }]}>
                        {formatDate(fee.timestamp)} at {formatTime(fee.timestamp)}
                      </ThemedText>
                      <ThemedText style={[styles.feeDuration, { color: colors.textMuted }]}>
                        {t('fees.usageDuration', { duration: fee.duration })}
                      </ThemedText>
                      {fee.paidAt && (
                        <ThemedText style={[styles.paidDate, { color: colors.secondary }]}>
                          {t('fees.paidOn', { date: formatDate(fee.paidAt) })}
                        </ThemedText>
                      )}
                    </ThemedView>
                    <ThemedText style={[styles.feeAmount, { color: colors.secondary }]}>
                      {formatCurrency(fee.amount)}
                    </ThemedText>
                  </ThemedView>
                </FuturisticCard>
              ))
            )}
          </ThemedView>
        </ThemedView>
      </ParallaxScrollView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  headerImageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    gap: getResponsiveSpacing('sm'),
  },
  headerTitle: {
    fontSize: getResponsiveFontSize('header'),
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 1,
  },
  headerSubtitle: {
    fontSize: getResponsiveFontSize('sm'),
    textAlign: 'center',
    opacity: 0.8,
    letterSpacing: 0.5,
  },
  content: {
    gap: getResponsiveSpacing('xl'),
    paddingBottom: getResponsiveSpacing('xl'),
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getResponsiveSpacing('sm'),
    justifyContent: 'space-between',
  },
  statCard: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: getResponsiveSpacing('xs'),
    flex: isSmallScreen ? 1 : 0,
    minWidth: isSmallScreen ? '48%' : 120,
    minHeight: responsiveDimensions.cardHeight.md,
  },
  statNumber: {
    fontSize: getResponsiveFontSize('xl'),
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  statLabel: {
    fontSize: getResponsiveFontSize('xs'),
    textAlign: 'center',
    opacity: 0.8,
    fontWeight: '500',
  },
  tabContainer: {
    flexDirection: 'row',
    gap: getResponsiveSpacing('md'),
    justifyContent: 'space-between',
  },
  tabButton: {
    flex: 1,
  },
  section: {
    gap: getResponsiveSpacing('md'),
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
    letterSpacing: 0.5,
    marginBottom: getResponsiveSpacing('xs'),
  },
  feeItem: {
    gap: getResponsiveSpacing('sm'),
    marginBottom: getResponsiveSpacing('sm'),
  },
  feeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  feeInfo: {
    flex: 1,
    gap: getResponsiveSpacing('xs'),
  },
  feeApp: {
    fontSize: getResponsiveFontSize('md'),
    fontWeight: '600',
  },
  feeDate: {
    fontSize: getResponsiveFontSize('xs'),
    opacity: 0.7,
  },
  feeDuration: {
    fontSize: getResponsiveFontSize('xs'),
    opacity: 0.7,
  },
  paidDate: {
    fontSize: getResponsiveFontSize('xs'),
    fontWeight: '600',
  },
  feeAmount: {
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '700',
  },
  feeActions: {
    flexDirection: isSmallScreen ? 'column' : 'row',
    gap: getResponsiveSpacing('sm'),
    justifyContent: 'flex-end',
  },
  actionButton: {
    minWidth: isSmallScreen ? '100%' : 100,
  },
  payAllButton: {
    alignSelf: 'center',
    minWidth: 200,
  },
  emptyState: {
    alignItems: 'center',
    padding: getResponsiveSpacing('xl'),
    gap: getResponsiveSpacing('md'),
  },
  emptyTitle: {
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
    textAlign: 'center',
    marginTop: getResponsiveSpacing('md'),
  },
  emptyDesc: {
    fontSize: getResponsiveFontSize('sm'),
    textAlign: 'center',
    opacity: 0.8,
  },
});
