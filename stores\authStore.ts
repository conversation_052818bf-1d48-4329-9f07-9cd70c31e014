import { User } from 'firebase/auth';
import { create } from 'zustand';
import { analyticsService } from '../services/analyticsService';
import { authService, UserProfile } from '../services/authService';

interface AuthState {
  user: User | null;
  userProfile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  
  // Actions
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, displayName?: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>;
  setUser: (user: User | null) => void;
  setUserProfile: (profile: UserProfile | null) => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  userProfile: null,
  isLoading: true,
  isAuthenticated: false,

  signIn: async (email: string, password: string) => {
    try {
      set({ isLoading: true });
      const user = await authService.signIn(email, password);
      const profile = await authService.getUserProfile(user.uid);

      // Track login event
      analyticsService.trackLogin();
      if (profile) {
        analyticsService.updateUserProperties(user.uid, profile);
      }

      set({
        user,
        userProfile: profile,
        isAuthenticated: true,
        isLoading: false
      });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  signUp: async (email: string, password: string, displayName?: string) => {
    try {
      set({ isLoading: true });
      const user = await authService.signUp(email, password, displayName);
      const profile = await authService.getUserProfile(user.uid);

      // Track signup event
      analyticsService.trackSignUp();
      if (profile) {
        analyticsService.updateUserProperties(user.uid, profile);
      }

      set({
        user,
        userProfile: profile,
        isAuthenticated: true,
        isLoading: false
      });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  signOut: async () => {
    try {
      set({ isLoading: true });

      // Track logout event
      analyticsService.trackLogout();

      await authService.signOut();
      set({
        user: null,
        userProfile: null,
        isAuthenticated: false,
        isLoading: false
      });
    } catch (error) {
      set({ isLoading: false });
      throw error;
    }
  },

  updateProfile: async (updates: Partial<UserProfile>) => {
    try {
      const { user, userProfile } = get();
      if (!user || !userProfile) return;

      await authService.updateUserProfile(user.uid, updates);
      const updatedProfile = await authService.getUserProfile(user.uid);
      
      set({ userProfile: updatedProfile });
    } catch (error) {
      throw error;
    }
  },

  setUser: (user: User | null) => {
    set({ 
      user, 
      isAuthenticated: !!user,
      isLoading: false 
    });
  },

  setUserProfile: (profile: UserProfile | null) => {
    set({ userProfile: profile });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },
}));
