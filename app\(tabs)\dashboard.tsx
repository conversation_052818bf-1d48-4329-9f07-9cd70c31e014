import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, TextStyle, ViewStyle } from 'react-native';

import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { FuturisticCard } from '@/components/ui/FuturisticCard';
import { FuturisticGrid } from '@/components/ui/FuturisticGrid';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAppTheme } from '@/contexts/ThemeContext';
import { useAppLockStore } from '@/stores/appLockStore';
import { useCurrency } from '../../utils/currency';
import { getResponsiveFontSize, getResponsiveSpacing, isSmallScreen, responsiveDimensions } from '../../utils/responsive';

export default function DashboardScreen() {
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t, i18n } = useTranslation();
  const { formatCurrency } = useCurrency();

  // Get data from store
  const {
    focusStreak,
    totalFocusHours,
    totalPaidAmount,
    paidPenalties,
    isLoading
  } = useAppLockStore();

  // Track screen view
  useEffect(() => {
    analyticsService.trackScreenView('Dashboard');
  }, []);

  // Calculate recent activity (last 5 penalties)
  const recentActivity = paidPenalties.slice(0, 5).map(penalty => ({
    id: penalty.id,
    app: penalty.appName,
    amount: penalty.amount,
    date: penalty.paidAt?.toLocaleDateString() || penalty.timestamp.toLocaleDateString(),
    icon: 'app.fill', // Default icon, could be enhanced with actual app icons
  }));

  return (
    <ThemedView style={styles.container}>
      <FuturisticGrid size={40} opacity={0.1} />

      <ParallaxScrollView
        headerBackgroundColor={{
          light: colors.background,
          dark: colors.background
        }}
        headerImage={
          <ThemedView style={styles.headerImageContainer}>
            <IconSymbol
              size={responsiveDimensions.header.iconSize}
              name="chart.bar.fill"
              color={colors.tint}
            />
            <ThemedText type="title" style={[styles.headerTitle, { color: colors.tint }]}>
              {t('dashboard.title')}
            </ThemedText>
            <ThemedText style={[styles.headerSubtitle, { color: colors.textSecondary }]}>
              {t('subtitles.dashboard')}
            </ThemedText>
          </ThemedView>
        }>

        <ThemedView style={styles.content}>
          {/* Primary Stats Grid */}
          <ThemedView style={styles.statsGrid}>
            <FuturisticCard variant="neon" style={styles.statCard} glowIntensity={1.2}>
              <IconSymbol size={24} name="flame.fill" color={colors.tint} />
              <ThemedText style={[styles.statNumber, { color: colors.tint }]}>
                {focusStreak}
              </ThemedText>
              <ThemedText style={styles.statLabel}>
                {t('dashboard.dayStreak')}
              </ThemedText>
            </FuturisticCard>

            <FuturisticCard variant="elevated" style={styles.statCard}>
              <IconSymbol size={24} name="clock.fill" color={colors.secondary} />
              <ThemedText style={[styles.statNumber, { color: colors.secondary }]}>
                {Math.round(totalFocusHours)}h
              </ThemedText>
              <ThemedText style={styles.statLabel}>
                {t('dashboard.focusHours')}
              </ThemedText>
            </FuturisticCard>

            <FuturisticCard variant="glass" style={styles.statCard}>
              <ThemedText style={[styles.statNumber, { color: colors.tint }]}>
                {formatCurrency(totalPaidAmount)}
              </ThemedText>
              <ThemedText style={styles.statLabel}>
                {t('dashboard.moneySaved')}
              </ThemedText>
            </FuturisticCard>


          </ThemedView>

          {/* Recent Activity Section */}
          <ThemedView style={styles.section}>
            <ThemedText style={[styles.sectionTitle, { color: colors.tint }]}>
              {t('dashboard.recentActivity')}
            </ThemedText>
            {recentActivity.length > 0 ? (
              recentActivity.map((penalty) => (
                <FuturisticCard
                  key={penalty.id}
                  variant="glass"
                  style={styles.activityItem}
                  glowIntensity={0.5}
                >
                  <ThemedView style={styles.activityHeader}>
                    <ThemedView style={styles.activityInfo}>
                      <IconSymbol size={20} name={penalty.icon as any} color={colors.textMuted} />
                      <ThemedText style={[styles.activityApp, { color: colors.text }]}>
                        {penalty.app}
                      </ThemedText>
                    </ThemedView>
                    <ThemedText style={[styles.activityAmount, { color: colors.textMuted }]}>
                      -{formatCurrency(penalty.amount)}
                    </ThemedText>
                  </ThemedView>
                  <ThemedText style={[styles.activityDate, { color: colors.textMuted }]}>
                    {penalty.date}
                  </ThemedText>
                </FuturisticCard>
              ))
            ) : (
              <FuturisticCard variant="glass" style={styles.activityItem}>
                <ThemedText style={[styles.activityApp, { color: colors.textMuted, textAlign: 'center' }]}>
                  {t('dashboard.noRecentActivity')}
                </ThemedText>
              </FuturisticCard>
            )}
          </ThemedView>




        </ThemedView>
      </ParallaxScrollView>
    </ThemedView>
  );
}

interface Styles {
  container: ViewStyle;
  headerImageContainer: ViewStyle;
  headerTitle: TextStyle;
  headerSubtitle: TextStyle;
  content: ViewStyle;
  statsGrid: ViewStyle;
  statCard: ViewStyle;
  statNumber: TextStyle;
  statLabel: TextStyle;
  section: ViewStyle;
  sectionTitle: TextStyle;
  activityItem: ViewStyle;
  activityHeader: ViewStyle;
  activityInfo: ViewStyle;
  activityApp: TextStyle;
  activityAmount: TextStyle;
  activityDate: TextStyle;
}

const styles = StyleSheet.create<Styles>({
  container: {
    flex: 1,
    position: 'relative',
  },
  headerImageContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    gap: getResponsiveSpacing('sm'),
  },
  headerTitle: {
    fontSize: getResponsiveFontSize('header'),
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 1,
  },
  headerSubtitle: {
    fontSize: getResponsiveFontSize('sm'),
    textAlign: 'center',
    opacity: 0.8,
    letterSpacing: 0.5,
  },
  content: {
    gap: getResponsiveSpacing('xl'),
    paddingBottom: getResponsiveSpacing('xl'),
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: getResponsiveSpacing('sm'),
    justifyContent: 'space-between',
  },
  statCard: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: getResponsiveSpacing('xs'),
    flex: isSmallScreen ? 1 : 0,
    minWidth: isSmallScreen ? '48%' : 120,
    minHeight: responsiveDimensions.cardHeight.md,
  },
  statNumber: {
    fontSize: getResponsiveFontSize('xl'),
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  statLabel: {
    fontSize: getResponsiveFontSize('xs'),
    textAlign: 'center',
    opacity: 0.8,
    fontWeight: '500',
  },
  section: {
    gap: getResponsiveSpacing('md'),
  },
  sectionTitle: {
    fontSize: getResponsiveFontSize('lg'),
    fontWeight: '600',
    letterSpacing: 0.5,
    marginBottom: getResponsiveSpacing('xs'),
  },
  activityItem: {
    gap: getResponsiveSpacing('xs'),
    marginBottom: getResponsiveSpacing('sm'),
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  activityInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: getResponsiveSpacing('sm'),
  },
  activityApp: {
    fontSize: getResponsiveFontSize('md'),
    fontWeight: '600',
  },
  activityAmount: {
    fontSize: getResponsiveFontSize('md'),
    fontWeight: '700',
  },
  activityDate: {
    fontSize: getResponsiveFontSize('xs'),
    opacity: 0.7,
  }
});