import { Colors } from '@/constants/Colors';
import { useAppTheme } from '@/contexts/ThemeContext';
import { BottomTabBarButtonProps } from '@react-navigation/bottom-tabs';
import { useEffect, useRef } from 'react';
import { Animated, Pressable, StyleSheet } from 'react-native';
import { ensureMinTouchTarget } from '../utils/responsive';

export function HapticTab({
  accessibilityState,
  style,
  onPress,
  children,
}: BottomTabBarButtonProps) {
  const focused = accessibilityState?.selected;
  const { colorScheme } = useAppTheme();
  const tintColor = Colors[colorScheme ?? 'light'].tint;

  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  // Run animation when focus changes
  useEffect(() => {
    if (focused) {
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1.1,
          friction: 5,
          tension: 40,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          friction: 5,
          tension: 40,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false,
        })
      ]).start();
    }
  }, [focused, scaleAnim, glowAnim]);

  // Create underline style based on focus state
  const underlineAnimatedStyle = {
    transform: [
      {
        scaleX: glowAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.7, 1],
        }),
      },
    ] as unknown as { scaleX: Animated.AnimatedInterpolation<number> }[],
    opacity: glowAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 1],
    }),
  };

  const underlineBaseStyle = {
    height: 3,
    backgroundColor: tintColor,
    position: 'absolute' as const,
    bottom: 0,
    left: '10%' as const,
    right: '10%' as const,
    borderRadius: 2,
  };

  // Create scale transform style (native driver)
  const scaleStyle = {
    transform: [{ scale: scaleAnim }]
  };

  const handlePress = (e: any) => {
    // Add a quick pulse animation when pressed
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: focused ? 1.1 : 1,
        friction: 5,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();

    // Call original onPress
    onPress?.(e);
  };

  return (
    <Animated.View style={[styles.container]}>
      <Animated.View style={scaleStyle}>
        <Pressable
          onPress={handlePress}
          style={({ pressed }) => [
            {
              opacity: pressed ? 0.8 : 1,
            },
            styles.button,
            style,
          ]}>
          {children}
          {focused && (
            <Animated.View 
              style={[
                styles.underline,
                underlineBaseStyle,
                underlineAnimatedStyle,
              ]}
            />
          )}
        </Pressable>
      </Animated.View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    minHeight: ensureMinTouchTarget(44),
    justifyContent: 'center',
  },
  button: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingBottom: 8,
    minHeight: ensureMinTouchTarget(44),
  },
  underline: {
    position: 'absolute',
    bottom: 0,
    left: '10%',
    right: '10%',
    height: 3,
    borderRadius: 2,
  },
});
