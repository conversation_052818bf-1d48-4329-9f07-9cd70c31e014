import { router } from 'expo-router';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, StyleSheet, TextInput } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { FuturisticButton } from '@/components/ui/FuturisticButton';
import { FuturisticCard } from '@/components/ui/FuturisticCard';
import { FuturisticGrid } from '@/components/ui/FuturisticGrid';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAppTheme } from '@/contexts/ThemeContext';
import { useAuthStore } from '@/stores/authStore';
import { getResponsiveFontSize, getResponsiveSpacing, responsiveDimensions } from '../../utils/responsive';

export default function LoginScreen() {
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useTranslation();
  const { signIn, isLoading } = useAuthStore();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleLogin = async () => {
    if (!email || !password) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    try {
      await signIn(email, password);
      router.replace('/(tabs)/dashboard');
    } catch (error: any) {
      Alert.alert('Login Failed', error.message || 'An error occurred during login');
    }
  };

  const handleSignUp = () => {
    router.push('/(auth)/signup');
  };

  return (
    <ThemedView style={styles.container}>
      <FuturisticGrid size={40} opacity={0.1} />
      
      <ThemedView style={styles.content}>
        <ThemedView style={styles.header}>
          <IconSymbol
            size={responsiveDimensions.header.iconSize}
            name="lock.shield.fill"
            color={colors.tint}
          />
          <ThemedText type="title" style={[styles.title, { color: colors.tint }]}>
            Welcome to FeeFence
          </ThemedText>
          <ThemedText style={[styles.subtitle, { color: colors.textSecondary }]}>
            Sign in to your account
          </ThemedText>
        </ThemedView>

        <FuturisticCard variant="glass" style={styles.formCard}>
          <ThemedView style={styles.form}>
            <ThemedView style={styles.inputGroup}>
              <ThemedText style={[styles.label, { color: colors.text }]}>
                Email
              </ThemedText>
              <TextInput
                style={[styles.input, { 
                  color: colors.text, 
                  borderColor: colors.border,
                  backgroundColor: colors.surface 
                }]}
                value={email}
                onChangeText={setEmail}
                placeholder="Enter your email"
                placeholderTextColor={colors.textMuted}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </ThemedView>

            <ThemedView style={styles.inputGroup}>
              <ThemedText style={[styles.label, { color: colors.text }]}>
                Password
              </ThemedText>
              <TextInput
                style={[styles.input, { 
                  color: colors.text, 
                  borderColor: colors.border,
                  backgroundColor: colors.surface 
                }]}
                value={password}
                onChangeText={setPassword}
                placeholder="Enter your password"
                placeholderTextColor={colors.textMuted}
                secureTextEntry
                autoCapitalize="none"
                autoCorrect={false}
              />
            </ThemedView>

            <FuturisticButton
              title="Sign In"
              variant="neon"
              size="large"
              onPress={handleLogin}
              disabled={isLoading}
              style={styles.loginButton}
            />

            <ThemedView style={styles.divider}>
              <ThemedText style={[styles.dividerText, { color: colors.textMuted }]}>
                Don't have an account?
              </ThemedText>
            </ThemedView>

            <FuturisticButton
              title="Create Account"
              variant="outline"
              size="large"
              onPress={handleSignUp}
              disabled={isLoading}
              style={styles.signupButton}
            />
          </ThemedView>
        </FuturisticCard>
      </ThemedView>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'relative',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    padding: getResponsiveSpacing('lg'),
    gap: getResponsiveSpacing('xl'),
  },
  header: {
    alignItems: 'center',
    gap: getResponsiveSpacing('md'),
  },
  title: {
    fontSize: getResponsiveFontSize('header'),
    fontWeight: '700',
    textAlign: 'center',
    letterSpacing: 1,
  },
  subtitle: {
    fontSize: getResponsiveFontSize('md'),
    textAlign: 'center',
    opacity: 0.8,
  },
  formCard: {
    padding: getResponsiveSpacing('xl'),
  },
  form: {
    gap: getResponsiveSpacing('lg'),
  },
  inputGroup: {
    gap: getResponsiveSpacing('sm'),
  },
  label: {
    fontSize: getResponsiveFontSize('md'),
    fontWeight: '600',
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: getResponsiveSpacing('md'),
    fontSize: getResponsiveFontSize('md'),
    minHeight: 50,
  },
  loginButton: {
    marginTop: getResponsiveSpacing('md'),
  },
  divider: {
    alignItems: 'center',
    marginVertical: getResponsiveSpacing('sm'),
  },
  dividerText: {
    fontSize: getResponsiveFontSize('sm'),
  },
  signupButton: {
    marginBottom: getResponsiveSpacing('md'),
  },
});
