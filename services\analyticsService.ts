import { logEvent, setUserId, setUserProperties } from 'firebase/analytics';
import { Platform } from 'react-native';
import { analytics } from '../config/firebase';

// Analytics events for FeeFence
export const AnalyticsEvents = {
  // Authentication
  SIGN_UP: 'sign_up',
  LOGIN: 'login',
  LOGOUT: 'logout',
  
  // App Blocking
  APP_BLOCKED: 'app_blocked',
  APP_UNBLOCKED: 'app_unblocked',
  MASTER_LOCK_ENABLED: 'master_lock_enabled',
  MASTER_LOCK_DISABLED: 'master_lock_disabled',
  
  // Penalties
  PENALTY_CREATED: 'penalty_created',
  PENALTY_PAID: 'penalty_paid',
  PENALTY_DISPUTED: 'penalty_disputed',
  
  // Focus Sessions
  FOCUS_SESSION_STARTED: 'focus_session_started',
  FOCUS_SESSION_ENDED: 'focus_session_ended',
  FOCUS_VIOLATION: 'focus_violation',
  
  // Settings
  THEME_CHANGED: 'theme_changed',
  LANGUAGE_CHANGED: 'language_changed',
  GLOW_EFFECTS_TOGGLED: 'glow_effects_toggled',
  
  // Engagement
  SCREEN_VIEW: 'screen_view',
  BUTTON_CLICK: 'button_click',
} as const;

class AnalyticsService {
  private isEnabled: boolean = Platform.OS === 'web' && !!analytics;

  // Track custom events
  trackEvent(eventName: string, parameters?: Record<string, any>) {
    if (!this.isEnabled || !analytics) return;
    
    try {
      logEvent(analytics, eventName, parameters);
    } catch (error) {
      console.warn('Analytics event failed:', error);
    }
  }

  // Set user ID for analytics
  setUserId(userId: string) {
    if (!this.isEnabled || !analytics) return;
    
    try {
      setUserId(analytics, userId);
    } catch (error) {
      console.warn('Analytics setUserId failed:', error);
    }
  }

  // Set user properties
  setUserProperties(properties: Record<string, any>) {
    if (!this.isEnabled || !analytics) return;
    
    try {
      setUserProperties(analytics, properties);
    } catch (error) {
      console.warn('Analytics setUserProperties failed:', error);
    }
  }

  // Authentication events
  trackSignUp(method: string = 'email') {
    this.trackEvent(AnalyticsEvents.SIGN_UP, { method });
  }

  trackLogin(method: string = 'email') {
    this.trackEvent(AnalyticsEvents.LOGIN, { method });
  }

  trackLogout() {
    this.trackEvent(AnalyticsEvents.LOGOUT);
  }

  // App blocking events
  trackAppBlocked(appName: string, penaltyAmount: number) {
    this.trackEvent(AnalyticsEvents.APP_BLOCKED, {
      app_name: appName,
      penalty_amount: penaltyAmount,
    });
  }

  trackAppUnblocked(appName: string) {
    this.trackEvent(AnalyticsEvents.APP_UNBLOCKED, {
      app_name: appName,
    });
  }

  trackMasterLockToggle(enabled: boolean) {
    this.trackEvent(enabled ? AnalyticsEvents.MASTER_LOCK_ENABLED : AnalyticsEvents.MASTER_LOCK_DISABLED);
  }

  // Penalty events
  trackPenaltyCreated(appName: string, amount: number, duration: number) {
    this.trackEvent(AnalyticsEvents.PENALTY_CREATED, {
      app_name: appName,
      amount,
      duration_minutes: duration,
    });
  }

  trackPenaltyPaid(appName: string, amount: number) {
    this.trackEvent(AnalyticsEvents.PENALTY_PAID, {
      app_name: appName,
      amount,
    });
  }

  trackPenaltyDisputed(appName: string, amount: number, reason: string) {
    this.trackEvent(AnalyticsEvents.PENALTY_DISPUTED, {
      app_name: appName,
      amount,
      dispute_reason: reason,
    });
  }

  // Focus session events
  trackFocusSessionStarted(blockedAppsCount: number) {
    this.trackEvent(AnalyticsEvents.FOCUS_SESSION_STARTED, {
      blocked_apps_count: blockedAppsCount,
    });
  }

  trackFocusSessionEnded(duration: number, violations: number, totalPenalty: number) {
    this.trackEvent(AnalyticsEvents.FOCUS_SESSION_ENDED, {
      duration_minutes: duration,
      violations_count: violations,
      total_penalty: totalPenalty,
    });
  }

  trackFocusViolation(appName: string, penaltyAmount: number) {
    this.trackEvent(AnalyticsEvents.FOCUS_VIOLATION, {
      app_name: appName,
      penalty_amount: penaltyAmount,
    });
  }

  // Settings events
  trackThemeChanged(themeName: string) {
    this.trackEvent(AnalyticsEvents.THEME_CHANGED, {
      theme_name: themeName,
    });
  }

  trackLanguageChanged(language: string) {
    this.trackEvent(AnalyticsEvents.LANGUAGE_CHANGED, {
      language,
    });
  }

  trackGlowEffectsToggled(enabled: boolean) {
    this.trackEvent(AnalyticsEvents.GLOW_EFFECTS_TOGGLED, {
      enabled,
    });
  }

  // Screen tracking
  trackScreenView(screenName: string) {
    this.trackEvent(AnalyticsEvents.SCREEN_VIEW, {
      screen_name: screenName,
    });
  }

  // Button clicks
  trackButtonClick(buttonName: string, screenName?: string) {
    this.trackEvent(AnalyticsEvents.BUTTON_CLICK, {
      button_name: buttonName,
      screen_name: screenName,
    });
  }

  // User properties for segmentation
  updateUserProperties(userId: string, userProfile: any) {
    this.setUserId(userId);
    this.setUserProperties({
      theme_preference: userProfile.preferences?.theme,
      language_preference: userProfile.preferences?.language,
      glow_effects_enabled: userProfile.preferences?.glowEnabled,
      master_lock_enabled: userProfile.settings?.masterLockEnabled,
      monthly_fee_limit: userProfile.settings?.monthlyFeeLimit,
    });
  }
}

export const analyticsService = new AnalyticsService();
