import { Colors } from '@/constants/Colors';
import { useAppTheme } from '@/contexts/ThemeContext';
import React, { useState } from 'react';
import { Animated, Platform, StyleSheet, ViewStyle } from 'react-native';
import { FuturisticTouchable } from '../FuturisticTouchable';

interface FuturisticCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  onPress?: () => void;
  variant?: 'default' | 'elevated' | 'outlined' | 'glass' | 'neon';
  glowIntensity?: number;
  animated?: boolean;
  disabled?: boolean;
}

export const FuturisticCard: React.FC<FuturisticCardProps> = ({
  children,
  style,
  onPress,
  variant = 'default',
  glowIntensity = 1,
  animated = true,
  disabled = false,
}) => {
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  // Enhance glow intensity in dark mode
  const enhancedGlowIntensity = colorScheme === 'dark' ? glowIntensity * 1.8 : glowIntensity;

  const [hoverAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(1));
  const [glowAnim] = useState(new Animated.Value(0));

  const handleHoverIn = () => {
    if (!animated || disabled) return;

    Animated.parallel([
      Animated.timing(hoverAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1.02,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(glowAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handleHoverOut = () => {
    if (!animated || disabled) return;

    Animated.parallel([
      Animated.timing(hoverAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(glowAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const getVariantStyles = () => {
    const baseStyle = {
      backgroundColor: 'transparent',
      borderBottomWidth: 1,
      borderLeftWidth: 0,
      borderRightWidth: 0,
      borderTopWidth: 0,
      borderRadius: 0,
    };

    switch (variant) {
      case 'elevated':
        return {
          ...baseStyle,
          borderBottomColor: colors.border,
          borderBottomWidth: 2,
        };

      case 'outlined':
        return {
          ...baseStyle,
          borderBottomColor: colors.border,
        };

      case 'glass':
        return {
          ...baseStyle,
          borderBottomColor: colorScheme === 'dark' 
            ? 'rgba(255, 255, 255, 0.2)' 
            : 'rgba(0, 0, 0, 0.1)',
          borderBottomWidth: 2,
        };

      case 'neon':
        return {
          ...baseStyle,
          borderBottomColor: colors.neon,
          borderBottomWidth: 2,
          shadowColor: colors.neon,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.7,
          shadowRadius: 4,
        };

      default:
        return {
          ...baseStyle,
          borderBottomColor: colors.border,
        };
    }
  };

  const animatedStyle = {
    transform: [{ scale: scaleAnim }],
    shadowOpacity: glowAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [colorScheme === 'dark' ? 0.15 : 0, 0.3 * enhancedGlowIntensity],
    }),
    shadowRadius: glowAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [colorScheme === 'dark' ? 8 : 0, 20 * enhancedGlowIntensity],
    }),
    borderColor: hoverAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [colors.border, colors.neon],
    }),
  };

  const cardStyle = [
    styles.card,
    getVariantStyles(),
    style,
  ] as any;

  const hoverProps = Platform.OS === 'web' ? {
    onMouseEnter: handleHoverIn,
    onMouseLeave: handleHoverOut,
  } : {};

  if (onPress) {
    return (
      <FuturisticTouchable
        onPress={onPress}
        style={[cardStyle, animatedStyle]}
        disabled={disabled}
        glowIntensity={enhancedGlowIntensity}
        {...hoverProps}
      >
        {children}
      </FuturisticTouchable>
    );
  }

  return (
    <Animated.View style={[cardStyle, animatedStyle]} {...hoverProps}>
      {children}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  card: {
    paddingVertical: 16,
    overflow: 'visible',
    position: 'relative',
    marginBottom: 4,
  },
});
