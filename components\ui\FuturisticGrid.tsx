import { Colors } from '@/constants/Colors';
import { useAppTheme } from '@/contexts/ThemeContext';
import React from 'react';
import { Dimensions, StyleSheet, View } from 'react-native';

interface FuturisticGridProps {
  size?: number;
  opacity?: number;
  animated?: boolean;
}

export const FuturisticGrid: React.FC<FuturisticGridProps> = ({
  size = 40,
  opacity = 0.3,
  animated = false,
}) => {
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { width, height } = Dimensions.get('window');

  // Create grid lines using View components
  const verticalLines = [];
  const horizontalLines = [];

  for (let i = 0; i <= width; i += size) {
    verticalLines.push(
      <View
        key={`v-${i}`}
        style={[
          styles.line,
          styles.verticalLine,
          {
            left: i,
            height: height,
            backgroundColor: colors.grid,
            opacity,
          },
        ]}
      />
    );
  }

  for (let i = 0; i <= height; i += size) {
    horizontalLines.push(
      <View
        key={`h-${i}`}
        style={[
          styles.line,
          styles.horizontalLine,
          {
            top: i,
            width: width,
            backgroundColor: colors.grid,
            opacity,
          },
        ]}
      />
    );
  }

  return (
    <View style={styles.container} pointerEvents="none">
      {verticalLines}
      {horizontalLines}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  line: {
    position: 'absolute',
  },
  verticalLine: {
    width: 0.5,
  },
  horizontalLine: {
    height: 0.5,
  },
});
