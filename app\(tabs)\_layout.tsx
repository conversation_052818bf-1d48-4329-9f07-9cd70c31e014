import { Tabs } from 'expo-router';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Dimensions, Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { useAppTheme } from '@/contexts/ThemeContext';
import { getCurrencyIcon } from '../../utils/currency';

export default function TabLayout() {
  const { colorScheme } = useAppTheme();
  const { t, i18n } = useTranslation();
  const { width } = Dimensions.get('window');
  const isSmallScreen = width < 375; // iPhone SE and smaller
  const iconSize = isSmallScreen ? 24 : 28;

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            position: 'absolute',
            height: isSmallScreen ? 70 : 80,
            backgroundColor: colorScheme === 'dark' ? 'rgba(0, 0, 0, 0.9)' : 'rgba(245, 243, 240, 0.9)',
            borderTopWidth: 1,
            borderTopColor: colorScheme === 'dark' ? 'rgba(192, 192, 192, 0.2)' : 'rgba(255, 107, 53, 0.2)',
            backdropFilter: 'blur(20px)',
          },
          default: {
            height: isSmallScreen ? 60 : 70,
            paddingBottom: isSmallScreen ? 5 : 10,
            paddingTop: isSmallScreen ? 5 : 10,
            backgroundColor: colorScheme === 'dark' ? '#000000' : '#F5F3F0',
            borderTopWidth: 1,
            borderTopColor: colorScheme === 'dark' ? '#333333' : '#E8E4E0',
          },
        }),
        tabBarLabelStyle: {
          fontSize: isSmallScreen ? 10 : 12,
          marginTop: isSmallScreen ? -2 : 0,
          fontWeight: '600',
          letterSpacing: 0.5,
        },
        tabBarActiveTintColor: colorScheme === 'dark' ? '#C0C0C0' : '#FF6B35',
        tabBarInactiveTintColor: colorScheme === 'dark' ? '#888888' : '#B2BEC3',
      }}>
      <Tabs.Screen
        name="index"
        options={{
          href: null, // Hide this tab from the tab bar
        }}
      />
      <Tabs.Screen
        name="lock"
        options={{
          title: t('tabs.lock'),
          tabBarIcon: ({ color }) => <IconSymbol size={iconSize} name="lock.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="fees"
        options={{
          title: t('tabs.fees'),
          tabBarIcon: ({ color }) => <IconSymbol size={iconSize} name={getCurrencyIcon(i18n.language)} color={color} />,
        }}
      />
      <Tabs.Screen
        name="dashboard"
        options={{
          title: t('tabs.dashboard'),
          tabBarIcon: ({ color }) => <IconSymbol size={iconSize} name="chart.bar.fill" color={color} />,
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: t('tabs.settings'),
          tabBarIcon: ({ color }) => <IconSymbol size={iconSize} name="gear" color={color} />,
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: t('tabs.explore'),
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="paperplane.fill" color={color} />,
          href: null, // Hide this tab from the tab bar
        }}
      />
    </Tabs>
  );
}
