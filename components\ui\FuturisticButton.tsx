import { Colors } from '@/constants/Colors';
import { useAppTheme } from '@/contexts/ThemeContext';
import React, { useEffect, useState } from 'react';
import { Animated, Platform, StyleSheet, TextStyle, TouchableOpacity, ViewStyle } from 'react-native';
import { getResponsiveFontSize, getResponsiveSpacing, responsiveDimensions } from '../../utils/responsive';

interface FuturisticButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'neon';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  glowIntensity?: number;
  icon?: React.ReactNode;
}

export const FuturisticButton: React.FC<FuturisticButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  textStyle,
  glowIntensity = 1,
  icon,
}) => {
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  // Enhance glow intensity in dark mode
  const enhancedGlowIntensity = colorScheme === 'dark' ? glowIntensity * 1.8 : glowIntensity;
  
  // Animations that will use native driver
  const [scaleAnim] = useState(new Animated.Value(1));
  const [rippleAnim] = useState(new Animated.Value(0));
  
  // Animations that will use JS driver
  const [glowOpacity] = useState(new Animated.Value(0));
  const [glowRadius] = useState(new Animated.Value(0));
  const [hoverAnim] = useState(new Animated.Value(0));
  
  // Derived values for shadow properties
  const [shadowOpacity, setShadowOpacity] = useState(0);
  const [shadowRadius, setShadowRadius] = useState(0);
  
  // Update shadow properties when glowAnim changes
  useEffect(() => {
    const baseOpacity = colorScheme === 'dark' ? 0.2 : 0;
    
    const glowOpacityListener = glowOpacity.addListener(({value}) => {
      setShadowOpacity(baseOpacity + (value * 0.5 * enhancedGlowIntensity));
    });
    
    const glowRadiusListener = glowRadius.addListener(({value}) => {
      setShadowRadius((colorScheme === 'dark' ? 8 : 0) + (value * 15 * enhancedGlowIntensity));
    });
    
    return () => {
      glowOpacity.removeListener(glowOpacityListener);
      glowRadius.removeListener(glowRadiusListener);
    };
  }, [glowOpacity, glowRadius, enhancedGlowIntensity, colorScheme]);

  const handlePressIn = () => {
    if (disabled || loading) return;
    
    // Native driver animations
    Animated.parallel([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(rippleAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
    
    // JS driver animations
    Animated.parallel([
      Animated.timing(glowOpacity, {
        toValue: 1,
        duration: 150,
        useNativeDriver: false,
      }),
      Animated.timing(glowRadius, {
        toValue: 1,
        duration: 150,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    if (disabled || loading) return;
    
    // Native driver animations
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 4,
        tension: 100,
        useNativeDriver: true,
      }),
      Animated.timing(rippleAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
    
    // JS driver animations
    Animated.parallel([
      Animated.timing(glowOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }),
      Animated.timing(glowRadius, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const handleHoverIn = () => {
    if (disabled || loading) return;
    
    Animated.timing(hoverAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const handleHoverOut = () => {
    if (disabled || loading) return;
    
    Animated.timing(hoverAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingHorizontal: getResponsiveSpacing('sm'),
          paddingVertical: getResponsiveSpacing('xs'),
          minHeight: responsiveDimensions.cardHeight.sm,
        };
      case 'large':
        return {
          paddingHorizontal: getResponsiveSpacing('lg'),
          paddingVertical: getResponsiveSpacing('md'),
          minHeight: responsiveDimensions.cardHeight.lg,
        };
      default:
        return {
          paddingHorizontal: getResponsiveSpacing('md'),
          paddingVertical: getResponsiveSpacing('sm'),
          minHeight: responsiveDimensions.cardHeight.md,
        };
    }
  };

  const getVariantStyles = () => {
    const baseStyle = {
      backgroundColor: 'transparent',
      borderBottomWidth: 2,
      borderLeftWidth: 0,
      borderRightWidth: 0,
      borderTopWidth: 0,
      borderRadius: 0,
    };

    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          borderBottomColor: colors.tint,
          textColor: colors.tint,
        };
      case 'secondary':
        return {
          ...baseStyle,
          borderBottomColor: colors.secondary,
          textColor: colors.secondary,
        };
      case 'outline':
        return {
          ...baseStyle,
          borderBottomColor: colors.tint,
          textColor: colors.tint,
        };
      case 'ghost':
        return {
          ...baseStyle,
          borderBottomColor: 'transparent',
          textColor: colors.tint,
        };
      case 'neon':
        return {
          ...baseStyle,
          borderBottomColor: colors.neon,
          textColor: colors.neon,
          shadowColor: colors.neon,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.7,
          shadowRadius: 4,
        };
      default:
        return {
          ...baseStyle,
          borderBottomColor: colors.tint,
          textColor: colors.tint,
        };
    }
  };

  const variantStyles = getVariantStyles();
  const sizeStyles = getSizeStyles();

  const animatedStyle = {
    transform: [
      { scale: scaleAnim },
      {
        scale: rippleAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [1, 1.05],
        }),
      },
    ],
  };
  
  const jsAnimatedStyle = {
    shadowOpacity,
    shadowRadius,
    borderBottomColor: hoverAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [variantStyles.borderBottomColor, colors.neon],
    }),
  };

  const textAnimatedStyle = {
    color: hoverAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [variantStyles.textColor, colors.neon],
    }),
  };
  
  const underlineAnimatedStyle = {
    transform: [
      {
        scaleX: hoverAnim.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1.1],
        }),
      },
    ],
  };

  const hoverProps = Platform.OS === 'web' ? {
    onMouseEnter: handleHoverIn,
    onMouseLeave: handleHoverOut,
  } : {};

  return (
    <TouchableOpacity
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      disabled={disabled || loading}
      activeOpacity={0.8}
      {...hoverProps}
    >
      <Animated.View style={[styles.button, sizeStyles, { opacity: disabled ? 0.5 : 1 }]}>
        <Animated.View
          style={[
            {
              paddingHorizontal: size === 'small' ? 8 : 16,
              paddingVertical: size === 'small' ? 4 : 8,
            },
            animatedStyle,
            jsAnimatedStyle,
            style,
          ]}
        >
          {icon && <Animated.View style={styles.icon}>{icon}</Animated.View>}
          <Animated.Text
            style={[
              styles.text,
              {
                fontSize: getResponsiveFontSize(size === 'small' ? 'sm' : size === 'large' ? 'lg' : 'md'),
                color: variantStyles.textColor,
              },
              textAnimatedStyle,
              textStyle,
            ]}
          >
            {loading ? '...' : title}
          </Animated.Text>
          <Animated.View 
            style={[
              {
                position: 'absolute',
                bottom: -2,
                left: 0,
                right: 0,
                height: 2,
                backgroundColor: variantStyles.borderBottomColor,
              },
              underlineAnimatedStyle
            ]} 
          />
        </Animated.View>
      </Animated.View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'visible',
    position: 'relative',
  },
  text: {
    fontWeight: '600',
    textAlign: 'center',
    position: 'relative',
    zIndex: 1,
  },
  icon: {
    marginRight: 8,
    zIndex: 1,
  },
});
