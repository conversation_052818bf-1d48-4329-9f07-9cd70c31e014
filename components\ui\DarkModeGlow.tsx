import { Colors } from '@/constants/Colors';
import { useAppTheme } from '@/contexts/ThemeContext';
import React from 'react';
import { StyleSheet, View } from 'react-native';

interface DarkModeGlowProps {
  children: React.ReactNode;
  intensity?: number;
  color?: string;
}

/**
 * A component that adds a subtle glow effect to its children when in dark mode
 */
export const DarkModeGlow: React.FC<DarkModeGlowProps> = ({
  children,
  intensity = 1,
  color,
}) => {
  const { colorScheme } = useAppTheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  // Only apply glow in dark mode
  if (colorScheme !== 'dark') {
    return <>{children}</>;
  }
  
  const glowColor = color || colors.neon;
  
  return (
    <View
      style={[
        styles.container,
        {
          shadowColor: glowColor,
          shadowOpacity: 0.3 * intensity,
          shadowRadius: 12 * intensity,
        },
      ]}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    shadowOffset: { width: 0, height: 0 },
    overflow: 'visible',
  },
});