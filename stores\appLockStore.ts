import { create } from 'zustand';
import { appLockService, BlockedApp, PenaltyRecord, FocusSession } from '../services/appLockService';

interface AppLockState {
  // State
  blockedApps: BlockedApp[];
  pendingPenalties: PenaltyRecord[];
  paidPenalties: PenaltyRecord[];
  focusSessions: FocusSession[];
  currentSession: FocusSession | null;
  isLoading: boolean;
  masterLockEnabled: boolean;

  // Computed values
  totalPendingAmount: number;
  totalPaidAmount: number;
  blockedAppsCount: number;
  focusStreak: number;
  totalFocusHours: number;

  // Actions
  loadUserData: (userId: string) => Promise<void>;
  addBlockedApp: (userId: string, appData: Omit<BlockedApp, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateBlockedApp: (appId: string, updates: Partial<BlockedApp>) => Promise<void>;
  deleteBlockedApp: (appId: string) => Promise<void>;
  toggleAppBlock: (appId: string) => Promise<void>;
  
  createPenalty: (userId: string, penaltyData: Omit<PenaltyRecord, 'id' | 'userId' | 'timestamp'>) => Promise<void>;
  payPenalty: (penaltyId: string) => Promise<void>;
  payAllPenalties: () => Promise<void>;
  disputePenalty: (penaltyId: string, reason: string) => Promise<void>;
  
  startFocusSession: (userId: string) => Promise<void>;
  endFocusSession: (violationsCount: number, totalPenalty: number) => Promise<void>;
  
  setMasterLock: (enabled: boolean) => void;
  setLoading: (loading: boolean) => void;
}

export const useAppLockStore = create<AppLockState>((set, get) => ({
  // Initial state
  blockedApps: [],
  pendingPenalties: [],
  paidPenalties: [],
  focusSessions: [],
  currentSession: null,
  isLoading: false,
  masterLockEnabled: false,

  // Computed values
  get totalPendingAmount() {
    return get().pendingPenalties.reduce((sum, penalty) => sum + penalty.amount, 0);
  },

  get totalPaidAmount() {
    return get().paidPenalties.reduce((sum, penalty) => sum + penalty.amount, 0);
  },

  get blockedAppsCount() {
    return get().blockedApps.filter(app => app.isBlocked).length;
  },

  get focusStreak() {
    const sessions = get().focusSessions;
    if (sessions.length === 0) return 0;
    
    // Calculate consecutive days with focus sessions
    let streak = 0;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    for (let i = 0; i < sessions.length; i++) {
      const sessionDate = new Date(sessions[i].startTime);
      sessionDate.setHours(0, 0, 0, 0);
      
      const daysDiff = Math.floor((today.getTime() - sessionDate.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysDiff === streak) {
        streak++;
      } else {
        break;
      }
    }
    
    return streak;
  },

  get totalFocusHours() {
    return get().focusSessions.reduce((total, session) => {
      return total + (session.duration || 0);
    }, 0) / 60; // Convert minutes to hours
  },

  // Actions
  loadUserData: async (userId: string) => {
    try {
      set({ isLoading: true });
      
      const [blockedApps, pendingPenalties, paidPenalties, focusSessions] = await Promise.all([
        appLockService.getUserBlockedApps(userId),
        appLockService.getUserPenalties(userId, 'pending'),
        appLockService.getUserPenalties(userId, 'paid'),
        appLockService.getUserFocusSessions(userId, 30), // Last 30 sessions
      ]);

      set({
        blockedApps,
        pendingPenalties,
        paidPenalties,
        focusSessions,
        isLoading: false,
      });
    } catch (error) {
      console.error('Load user data error:', error);
      set({ isLoading: false });
      throw error;
    }
  },

  addBlockedApp: async (userId: string, appData) => {
    try {
      const appId = await appLockService.addBlockedApp(userId, appData);
      const newApp: BlockedApp = {
        id: appId,
        userId,
        ...appData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      set(state => ({
        blockedApps: [newApp, ...state.blockedApps]
      }));
    } catch (error) {
      console.error('Add blocked app error:', error);
      throw error;
    }
  },

  updateBlockedApp: async (appId: string, updates) => {
    try {
      await appLockService.updateBlockedApp(appId, updates);
      
      set(state => ({
        blockedApps: state.blockedApps.map(app =>
          app.id === appId ? { ...app, ...updates, updatedAt: new Date() } : app
        )
      }));
    } catch (error) {
      console.error('Update blocked app error:', error);
      throw error;
    }
  },

  deleteBlockedApp: async (appId: string) => {
    try {
      await appLockService.deleteBlockedApp(appId);
      
      set(state => ({
        blockedApps: state.blockedApps.filter(app => app.id !== appId)
      }));
    } catch (error) {
      console.error('Delete blocked app error:', error);
      throw error;
    }
  },

  toggleAppBlock: async (appId: string) => {
    const { blockedApps, updateBlockedApp } = get();
    const app = blockedApps.find(a => a.id === appId);
    
    if (app) {
      await updateBlockedApp(appId, { isBlocked: !app.isBlocked });
    }
  },

  createPenalty: async (userId: string, penaltyData) => {
    try {
      const penaltyId = await appLockService.createPenalty(userId, penaltyData);
      const newPenalty: PenaltyRecord = {
        id: penaltyId,
        userId,
        ...penaltyData,
        timestamp: new Date(),
      };
      
      set(state => ({
        pendingPenalties: [newPenalty, ...state.pendingPenalties]
      }));
    } catch (error) {
      console.error('Create penalty error:', error);
      throw error;
    }
  },

  payPenalty: async (penaltyId: string) => {
    try {
      await appLockService.payPenalty(penaltyId);
      
      set(state => {
        const penalty = state.pendingPenalties.find(p => p.id === penaltyId);
        if (penalty) {
          const paidPenalty = { ...penalty, status: 'paid' as const, paidAt: new Date() };
          return {
            pendingPenalties: state.pendingPenalties.filter(p => p.id !== penaltyId),
            paidPenalties: [paidPenalty, ...state.paidPenalties]
          };
        }
        return state;
      });
    } catch (error) {
      console.error('Pay penalty error:', error);
      throw error;
    }
  },

  payAllPenalties: async () => {
    try {
      const { pendingPenalties } = get();
      
      await Promise.all(
        pendingPenalties.map(penalty => appLockService.payPenalty(penalty.id))
      );
      
      set(state => ({
        paidPenalties: [
          ...state.pendingPenalties.map(p => ({ ...p, status: 'paid' as const, paidAt: new Date() })),
          ...state.paidPenalties
        ],
        pendingPenalties: []
      }));
    } catch (error) {
      console.error('Pay all penalties error:', error);
      throw error;
    }
  },

  disputePenalty: async (penaltyId: string, reason: string) => {
    try {
      await appLockService.disputePenalty(penaltyId, reason);
      
      set(state => ({
        pendingPenalties: state.pendingPenalties.map(penalty =>
          penalty.id === penaltyId 
            ? { ...penalty, status: 'disputed' as const, disputeReason: reason }
            : penalty
        )
      }));
    } catch (error) {
      console.error('Dispute penalty error:', error);
      throw error;
    }
  },

  startFocusSession: async (userId: string) => {
    try {
      const { blockedAppsCount } = get();
      const sessionId = await appLockService.startFocusSession(userId, blockedAppsCount);
      
      const newSession: FocusSession = {
        id: sessionId,
        userId,
        startTime: new Date(),
        blockedAppsCount,
        violationsCount: 0,
        totalPenalty: 0,
      };
      
      set({ currentSession: newSession });
    } catch (error) {
      console.error('Start focus session error:', error);
      throw error;
    }
  },

  endFocusSession: async (violationsCount: number, totalPenalty: number) => {
    try {
      const { currentSession } = get();
      if (!currentSession) return;
      
      await appLockService.endFocusSession(currentSession.id, violationsCount, totalPenalty);
      
      const endTime = new Date();
      const duration = Math.round((endTime.getTime() - currentSession.startTime.getTime()) / (1000 * 60));
      
      const completedSession: FocusSession = {
        ...currentSession,
        endTime,
        duration,
        violationsCount,
        totalPenalty,
      };
      
      set(state => ({
        currentSession: null,
        focusSessions: [completedSession, ...state.focusSessions]
      }));
    } catch (error) {
      console.error('End focus session error:', error);
      throw error;
    }
  },

  setMasterLock: (enabled: boolean) => {
    set({ masterLockEnabled: enabled });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },
}));
